<template>
  <view class="agent-page">
    <!-- 顶部切换和搜索 -->
    <view class="header-section">
      <view class="tab-search-row">
        <!-- 储备/正式切换 -->
        <view class="tab-container">
          <view
            class="tab-item"
            :class="{ active: activeTab === 'reserve' }"
            @click="switchTab('reserve')"
          >
            储备
          </view>
          <view
            class="tab-item"
            :class="{ active: activeTab === 'formal' }"
            @click="switchTab('formal')"
          >
            正式
          </view>
        </view>

        <!-- 搜索框 -->
        <view class="search-container">
          <van-field
            v-model="searchKeyword"
            placeholder=""
            border="{{ false }}"
            input-align="left"
            @input="onSearch"
          >
            <van-icon slot="left-icon" name="search" />
          </van-field>
          <view @click="onSearch" class="search-text">搜索</view>
        </view>
      </view>

      <!-- 省市选择 -->
      <view class="picker-row">
        <view class="picker-item" @click="showProvincePicker = true">
          <text>{{ selectedProvince || "全部省份" }}</text>
          <van-icon name="arrow-down" />
        </view>
        <view class="picker-item" @click="showCityPicker = true">
          <text>{{ selectedCity || "全部城市" }}</text>
          <van-icon name="arrow-down" />
        </view>
      </view>
    </view>

    <!-- 列表内容 -->
    <div class="list-wrapper">
      <scroll-view
        class="list-container"
        scroll-y="true"
        @scrolltolower="loadMore"
        lower-threshold="200"
      >
        <view class="agent-list">
          <view
            v-for="(item, index) in agentList"
            :key="item.id"
            class="agent-item"
          >
            <!-- 左滑操作区域 -->
            <van-swipe-cell>
              <view class="agent-content" @click="goToDetail(item)">
                <view class="agent-info">
                  <view class="company-name">{{ item.companyName }}</view>
                  <view class="location"
                    >{{ item.province }}/{{ item.city }}</view
                  >
                  <view class="contact">
                    <text>联系电话：</text>
                    <text>{{ item.phone }}</text>
                  </view>
                </view>
                <view class="detail-btn" @click.stop="goToDetail(item)">
                  查看详情
                </view>
              </view>

              <!-- 右滑操作按钮 -->
              <view slot="right" class="swipe-actions">
                <view class="action-btn edit-btn" @click="editAgent(item)">
                  <van-icon name="edit" />
                  <text>编辑</text>
                </view>
                <view class="action-btn delete-btn" @click="deleteAgent(item)">
                  <van-icon name="delete" />
                  <text>删除</text>
                </view>
              </view>
            </van-swipe-cell>
          </view>
        </view>

        <!-- 加载更多 -->
        <view v-if="loading" class="loading-more">
          <van-loading size="16px">加载中...</van-loading>
        </view>

        <!-- 没有更多数据 -->
        <view v-if="!hasMore && agentList.length > 0" class="no-more"></view>

        <!-- 空状态 -->
        <view v-if="agentList.length === 0 && !loading" class="task-panel">
          <image
            class="empty"
            src="/static/image/bgs/empty.png"
            mode=""
          ></image>
          <view class="empty-text">暂无数据</view>
        </view>
      </scroll-view>
    </div>

    <!-- 底部悬浮按钮 -->
    <view class="float-btn" @click="addAgent">
      <van-icon name="plus" />
      <text>新增储备代理商代表</text>
    </view>

    <!-- 省份选择器 -->
    <van-popup
      v-model="showProvincePicker"
      position="bottom"
      custom-style="height: 40%"
    >
      <van-picker
        :columns="provinceColumns"
        @confirm="onProvinceConfirm"
        @cancel="showProvincePicker = false"
        show-toolbar
        title="选择省份"
      />
    </van-popup>

    <!-- 城市选择器 -->
    <van-popup
      v-model="showCityPicker"
      position="bottom"
      custom-style="height: 40%"
    >
      <van-picker
        :columns="cityColumns"
        @confirm="onCityConfirm"
        @cancel="showCityPicker = false"
        show-toolbar
        title="选择城市"
      />
    </van-popup>
  </view>
</template>

<script>
import {
  getAgentList,
  deleteAgent as deleteAgentApi,
} from "@/common/api/agentDA/index.js";
import { getProvinceList, getCityList } from "@/common/data/area.js";

export default {
  data() {
    return {
      // 当前选中的tab
      activeTab: "reserve", // reserve: 储备, formal: 正式

      // 搜索关键词
      searchKeyword: "",

      // 省市选择
      showProvincePicker: false,
      showCityPicker: false,
      selectedProvince: "",
      selectedCity: "",
      selectedProvinceCode: "",
      selectedCityCode: "",

      // 省市数据
      provinceColumns: [],
      cityColumns: [],

      // 列表数据
      agentList: [],
      loading: false,
      refreshing: false,
      hasMore: true,

      // 分页参数
      pageNum: 1,
      pageSize: 10,

      // 搜索防抖定时器
      searchTimer: null,
    };
  },

  onLoad() {
    this.initProvinceData();
    this.loadAgentList();
  },

  methods: {
    // 初始化省份数据
    initProvinceData() {
      const provinces = getProvinceList();
      this.provinceColumns = [
        { text: "全部省份", value: "" },
        ...provinces.map((item) => ({
          text: item.name,
          value: item.code,
        })),
      ];
    },

    // 切换tab
    switchTab(tab) {
      if (this.activeTab === tab) return;
      this.activeTab = tab;
      this.resetList();
      this.loadAgentList();
    },

    // 搜索
    onSearch() {
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
      }
      this.searchTimer = setTimeout(() => {
        this.resetList();
        this.loadAgentList();
      }, 500);
    },

    // 省份选择确认
    onProvinceConfirm(value) {
      const selectedItem = this.provinceColumns.find(
        (item) => item.value === value
      );
      this.selectedProvince = selectedItem ? selectedItem.text : "";
      this.selectedProvinceCode = value;
      this.showProvincePicker = false;

      // 重置城市选择
      this.selectedCity = "";
      this.selectedCityCode = "";

      // 更新城市数据
      if (value) {
        const cities = getCityList(value);
        this.cityColumns = [
          { text: "全部城市", value: "" },
          ...cities.map((item) => ({
            text: item.name,
            value: item.code,
          })),
        ];
      } else {
        this.cityColumns = [{ text: "全部城市", value: "" }];
      }

      // 重新加载数据
      this.resetList();
      this.loadAgentList();
    },

    // 城市选择确认
    onCityConfirm(value) {
      const selectedItem = this.cityColumns.find(
        (item) => item.value === value
      );
      this.selectedCity = selectedItem ? selectedItem.text : "";
      this.selectedCityCode = value;
      this.showCityPicker = false;

      // 重新加载数据
      this.resetList();
      this.loadAgentList();
    },

    // 重置列表
    resetList() {
      this.agentList = [];
      this.pageNum = 1;
      this.hasMore = true;
    },

    // 加载代理商列表
    async loadAgentList() {
      if (this.loading) return;

      this.loading = true;

      try {
        const params = {
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          type: this.activeTab, // reserve: 储备, formal: 正式
          keyword: this.searchKeyword,
          provinceCode: this.selectedProvinceCode,
          cityCode: this.selectedCityCode,
        };

        const res = await getAgentList(params);

        if (res.code === 200) {
          const newList = res.data.list || [];

          if (this.pageNum === 1) {
            this.agentList = newList;
          } else {
            this.agentList = [...this.agentList, ...newList];
          }

          // 判断是否还有更多数据
          this.hasMore = newList.length === this.pageSize;
        } else {
          uni.showToast({
            title: res.message || "加载失败",
            icon: "none",
          });
        }
      } catch (error) {
        console.error("加载代理商列表失败:", error);
        uni.showToast({
          title: "网络错误",
          icon: "none",
        });
      } finally {
        this.loading = false;
        this.refreshing = false;
      }
    },

    // 加载更多
    loadMore() {
      if (!this.hasMore || this.loading) return;
      this.pageNum++;
      this.loadAgentList();
    },

    // 下拉刷新
    onRefresh() {
      this.refreshing = true;
      this.resetList();
      this.loadAgentList();
    },

    // 新增代理商
    addAgent() {
      uni.navigateTo({
        url: "/subPackage/agentDA/add",
      });
    },

    // 编辑代理商
    editAgent(item) {
      uni.navigateTo({
        url: `/subPackage/agentDA/add?id=${item.id}&mode=edit`,
      });
    },

    // 删除代理商
    deleteAgent(item) {
      uni.showModal({
        title: "提示",
        content: "确定要删除该代理商吗？",
        success: async (res) => {
          if (res.confirm) {
            try {
              const result = await deleteAgentApi(item.id);
              if (result.code === 200) {
                uni.showToast({
                  title: "删除成功",
                  icon: "success",
                });
                // 重新加载列表
                this.resetList();
                this.loadAgentList();
              } else {
                uni.showToast({
                  title: result.message || "删除失败",
                  icon: "none",
                });
              }
            } catch (error) {
              console.error("删除代理商失败:", error);
              uni.showToast({
                title: "网络错误",
                icon: "none",
              });
            }
          }
        },
      });
    },

    // 查看详情
    goToDetail(item) {
      const detailPage = this.activeTab === "reserve" ? "detail" : "detailZ";
      uni.navigateTo({
        url: `/subPackage/agentDA/${detailPage}?id=${item.id}`,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.empty {
  width: 230rpx;
  height: 200rpx;
  margin: 26vh auto 20rpx;
  display: flex;
}
.empty-text {
  color: var(---white, #fff);
  text-align: center;

  /* 点文本-加粗/12pt bold */
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 600;
  line-height: 20px; /* 166.667% */
}
.agent-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.tab-search-row {
  display: flex;
  align-items: center;
  padding: 30rpx 20rpx;
  background: rgba(255, 255, 255, 0.9);

  /* 下层投影 */
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.12);
}

.tab-container {
  display: flex;
  padding: 6rpx;
}

.tab-item {
  display: flex;
  padding: 6rpx 22rpx;
  justify-content: center;
  align-items: center;
  gap: 10px;
  font-size: 28rpx;
  color: #4068f5;
  transition: all 0.3s;
  border-radius: 52rpx;
  background: #fff;
  margin-right: 10rpx;

  &.active {
    background: #4285f4;
    color: #fff;
  }
}

.search-container {
  flex: 1;
  background: #fff;
  border-radius: 50rpx;
  overflow: hidden;
  display: flex;
  align-items: center;
  border: 1px solid #4068f5;
  .search-text {
    width: 100rpx;
    height: 64rpx;
    flex-shrink: 0;
    border-radius: 70rpx;
    background: #4068f5;
    color: #fff;

    /* 点文本-常规/14pt regular */
    font-family: "PingFang SC";
    font-size: 28rpx;
    font-style: normal;
    font-weight: 400;
    text-align: center;
    line-height: 64rpx;
    margin-left: 20rpx;
  }
}

.picker-row {
  display: flex;
  margin-top: 20rpx;
  margin-left: 20rpx;
  margin-bottom: 20rpx;
}

.picker-item {
  display: flex;
  width: 180rpx;
  height: 48rpx;
  justify-content: center;
  align-items: center;
  gap: 2px;
  flex-shrink: 0;
  border-radius: 48rpx;
  background: rgba(255, 255, 255, 0.9);

  /* 下层投影 */
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.12);
  margin-right: 20rpx;
  color: #8d9094;
  /* 点文本-常规/14pt regular */
  font-family: "PingFang SC";
  font-size: 28rpx;
  font-style: normal;
  font-weight: 400;
}
.list-wrapper {
  flex: 1;
  overflow-y: hidden;
  padding: 0 20rpx;
}
.list-container {
  height: 100%;
}

.agent-item {
  margin-bottom: 20rpx;
  border-radius: 20rpx;
  background: rgba(255, 255, 255, 0.9);

  /* 下层投影 */
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.12);
}

.agent-content {
  display: flex;
  align-items: center;
  padding: 30rpx;
}

.agent-info {
  flex: 1;
}

.company-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
}

.location {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.contact {
  font-size: 26rpx;
  color: #666;
}

.detail-btn {
  color: #fff;

  /* 点文本-常规/10pt regular */
  font-family: "PingFang SC";
  font-size: 20rpx;
  font-style: normal;
  font-weight: 400;
  line-height: 36rpx; /* 180% */
  display: inline-flex;
  padding: 2rpx 16rpx;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 130rpx;
  background: linear-gradient(180deg, #52acff -12%, #263af0 117%);
  margin-top: 74rpx;
}

.swipe-actions {
  display: flex;
  height: 100%;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 120rpx;
  font-size: 24rpx;
  color: #fff;

  &.edit-btn {
    background: #4285f4;
  }

  &.delete-btn {
    background: #ff4757;
  }
}

.loading-more,
.no-more {
  text-align: center;
  padding: 40rpx;
  font-size: 26rpx;
  color: #999;
}

.empty-state {
  padding: 100rpx 0;
}

.float-btn {
  box-sizing: border-box;
  width: 94%;
  position: fixed;
  bottom: 40rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 40rpx;
  background: #4285f4;
  color: #fff;
  border-radius: 8rpx;
  font-size: 28rpx;
  box-shadow: 0 4rpx 20rpx rgba(66, 133, 244, 0.3);
  z-index: 100;

  text {
    margin-left: 12rpx;
  }
}
</style>
